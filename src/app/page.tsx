"use client";
import { useState, useEffect } from "react";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

export default function Home() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  return (
    <Providers>
      <div className="min-h-screen bg-chillhouse-bg flex items-center justify-center py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Random Chillhouse Characters Background */}
        <div className="absolute inset-0 z-0">
          {/* Character 1 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-20 h-auto transform rotate-12"
            style={{ top: '8%', left: '5%' }}
          />
          {/* Character 2 */}
          <img
            src="/chillhouse2.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-24 h-auto transform -rotate-6"
            style={{ top: '18%', right: '8%' }}
          />
          {/* Character 3 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-18 h-auto transform rotate-45"
            style={{ bottom: '15%', left: '10%' }}
          />
          {/* Character 4 */}
          <img
            src="/chillhouse2.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-22 h-auto transform -rotate-12"
            style={{ bottom: '25%', right: '15%' }}
          />
          {/* Character 5 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-16 h-auto transform rotate-90"
            style={{ top: '60%', left: '3%' }}
          />
          {/* Character 6 */}
          <img
            src="/chillhouse2.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-28 h-auto transform -rotate-30"
            style={{ top: '40%', right: '3%' }}
          />
          {/* Character 7 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-20 h-auto transform rotate-15"
            style={{ top: '80%', left: '20%' }}
          />
          {/* Character 8 */}
          <img
            src="/chillhouse2.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-24 h-auto transform -rotate-45"
            style={{ top: '5%', left: '40%' }}
          />
          {/* Character 9 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-22 h-auto transform rotate-30"
            style={{ top: '35%', left: '15%' }}
          />
          {/* Character 10 */}
          <img
            src="/chillhouse2.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-26 h-auto transform -rotate-60"
            style={{ bottom: '40%', right: '25%' }}
          />
          {/* Character 11 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-18 h-auto transform rotate-75"
            style={{ top: '70%', right: '10%' }}
          />
          {/* Character 12 */}
          <img
            src="/chillhouse2.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-20 h-auto transform -rotate-20"
            style={{ bottom: '60%', left: '25%' }}
          />
          {/* Character 13 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-24 h-auto transform rotate-50"
            style={{ top: '25%', left: '60%' }}
          />
          {/* Character 14 */}
          <img
            src="/chillhouse2.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-16 h-auto transform -rotate-80"
            style={{ bottom: '10%', right: '40%' }}
          />
          {/* Character 15 */}
          <img
            src="/chillhouse.webp"
            alt="Chill House Character"
            className="absolute opacity-12 w-22 h-auto transform rotate-10"
            style={{ top: '50%', right: '35%' }}
          />
        </div>

        {/* Main Content */}
        <div className="w-full relative z-30">
          <BridgeInterface />
        </div>
      </div>
    </Providers>
  );
}
