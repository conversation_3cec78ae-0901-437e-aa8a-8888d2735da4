"use client";
import { useState, useEffect } from "react";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

export default function Home() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  return (
    <Providers>
      <div className="min-h-screen bg-black flex items-center justify-center py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Banner Background */}
        <div className="absolute inset-0 opacity-20">
          <img
            src="/banner.jpg"
            alt="Background"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Animated Stars */}
        <div className="absolute inset-0 z-10">
          <img
            src="/stars.gif"
            alt="Stars"
            className="w-full h-full object-cover opacity-30"
          />
        </div>

        {/* Main Content */}
        <div className="w-full relative z-20">
          <BridgeInterface />
        </div>
      </div>
    </Providers>
  );
}
